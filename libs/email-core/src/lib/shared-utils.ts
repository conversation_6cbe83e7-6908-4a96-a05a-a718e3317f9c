// Temporary copy of shared utilities to resolve TypeScript issues
// TODO: Refactor to use proper library imports later

import * as admin from 'firebase-admin';

// Logger utility (simplified version for email-core)
export interface LogDetails {
  [key: string]: any;
}

export class Logger {
  private env: string;

  constructor() {
    this.env = process.env['NODE_ENV'] || 'development';
  }

  info(message: string, details: LogDetails = {}): void {
    console.log(JSON.stringify({ severity: 'INFO', message, ...details, timestamp: new Date().toISOString() }));
  }

  error(message: string, details: LogDetails = {}): void {
    console.error(JSON.stringify({ severity: 'ERROR', message, ...details, timestamp: new Date().toISOString() }));
  }

  warn(message: string, details: LogDetails = {}): void {
    console.log(JSON.stringify({ severity: 'WARNING', message, ...details, timestamp: new Date().toISOString() }));
  }

  debug(message: string, details: LogDetails = {}): void {
    if (this.env !== 'production') {
      console.log(JSON.stringify({ severity: 'DEBUG', message, ...details, timestamp: new Date().toISOString() }));
    }
  }
}

export const logger = new Logger();

// Sanitize utilities
export function sanitizeId(id: string): string {
  return id.replace(/[^a-zA-Z0-9-]/g, '_');
}

export function sanitizeEmail(email: string): string {
  return email.replace(/[^a-zA-Z0-9@._-]/g, '_').toLowerCase();
}

// Config constants
export const SCOPES = [
  'https://www.googleapis.com/auth/gmail.readonly',
  'https://www.googleapis.com/auth/gmail.labels',
  'https://www.googleapis.com/auth/gmail.modify'
];

export const DDJS_LABEL_PREFIX = 'DDJS/';

export const LABEL_VALUES = [
  'not_job_search_related',
  'inbound_job_opportunity',
  'thanks_for_applying_or_application_received_confirmation',
  'rejection_role_paused_or_closed',
  'rejection_role_filled',
  'rejection_other',
  'next_steps_online_assement',
  'next_steps_interview_coordination',
  'next_steps_other',
  'offer',
  'other'
] as const;

export type LabelValue = typeof LABEL_VALUES[number];

export const OPENAI = {
  MODEL: 'gpt-4o-mini',
  TEMPERATURE: 0,
  MAX_TOKENS: 5000
} as const;

// Database utility
export type DatabaseType = 'emulator' | 'production' | 'development';

export class Database {
  public db!: admin.firestore.Firestore;
  public admin!: typeof admin;
  public databaseType!: DatabaseType;

  constructor() {
    this._initializeDatabase();
  }

  private _initializeDatabase(): void {
    const useProduction = process.env['USE_PRODUCTION_FIRESTORE'] === 'true';
    const projectId = useProduction || process.env['NODE_ENV'] === 'production'
      ? 'data-driven-job-search'
      : 'ddjs-dev-458016';

    if (process.env['FIRESTORE_EMULATOR_HOST']) {
      console.log(`Using Firestore emulator at ${process.env['FIRESTORE_EMULATOR_HOST']} with project ID: ${projectId}`);

      if (!admin.apps.length) {
        admin.initializeApp({
          projectId: projectId
        });

        const db = admin.firestore();
        db.settings({
          host: process.env['FIRESTORE_EMULATOR_HOST'],
          ssl: false
        });
      }

      this.db = admin.firestore();
      this.admin = admin;
      this.databaseType = 'emulator';

    } else if (useProduction || process.env['NODE_ENV'] === 'production') {
      console.log('Using production Firestore database with project ID: data-driven-job-search');

      if (!admin.apps.length) {
        admin.initializeApp({
          credential: admin.credential.applicationDefault(),
          projectId: 'data-driven-job-search'
        });
      }

      this.db = admin.firestore();
      this.admin = admin;
      this.databaseType = 'production';

    } else {
      console.log(`Using development Firestore database with project ID: ${projectId}`);

      if (!admin.apps.length) {
        admin.initializeApp({
          credential: admin.credential.applicationDefault(),
          projectId: projectId
        });
      }

      this.db = admin.firestore();
      this.admin = admin;
      this.databaseType = 'development';
    }
  }

  collection(name: string): admin.firestore.CollectionReference {
    return this.db.collection(name);
  }

  runTransaction(updateFunction: (transaction: admin.firestore.Transaction) => Promise<any>): Promise<any> {
    return this.db.runTransaction(updateFunction);
  }
}
