import { create<PERSON>lerk<PERSON>lient } from '@clerk/nextjs/server';
import { google } from 'googleapis';
import { Logger } from './logger';
import { GmailService } from './gmail.service';

interface UserEmail {
  address: string;
  verified: boolean;
  primary: boolean;
}

export class AuthService {
  private logger: Logger;
  private clerkClient: any;

  constructor(logger: Logger) {
    this.logger = logger;

    // Initialize clerk client with secret key
    this.clerkClient = createClerkClient({
      secretKey: process.env['CLERK_SECRET_KEY']
    });

    if (!process.env['CLERK_SECRET_KEY']) {
      this.logger.error('Missing CLERK_SECRET_KEY environment variable');
    }
  }

  async getClerkUserIdByEmail(email: string): Promise<string> {
    try {
      this.logger.info('Fetching Clerk user by email', { email });
      const users = await this.clerkClient.users.getUserList({
        emailAddress: [email],
        limit: 1
      });

      if (!users || users.length === 0) {
        throw new Error('No user found with that email');
      }

      const userId = users[0].id;
      this.logger.info('Found Clerk user', { email, userId });
      return userId;
    } catch (error) {
      this.logger.error('Error fetching user by email:', {
        email,
        error: error instanceof Error ? error.message : String(error)
      });
      throw new Error('Could not retrieve user information');
    }
  }

  /**
   * Creates a Gmail service client for a user using Clerk-managed OAuth tokens.
   *
   * IMPORTANT: This method uses Clerk's OAuth token management, which eliminates
   * the need for GOOGLE_CLIENT_ID and GOOGLE_CLIENT_SECRET environment variables.
   *
   * Authentication Flow:
   * 1. User authenticates with Google through Clerk's OAuth flow
   * 2. Clerk stores and manages OAuth tokens (access + refresh)
   * 3. We retrieve tokens from Clerk using getUserOauthAccessToken()
   * 4. We create a Gmail client using the retrieved tokens
   *
   * No direct OAuth client credentials are required in our application.
   */
  async getGmailClientForUser(userId: string): Promise<GmailService> {
    try {
      this.logger.info('Fetching OAuth tokens from Clerk', { userId });

      // First check if the user has connected their Google account
      const user = await this.clerkClient.users.getUser(userId);
      const googleAccount = user.externalAccounts.find(
        (account: any) => account.provider === 'oauth_google'
      );

      if (!googleAccount) {
        this.logger.error('No Google account connected', { userId });
        throw new Error('User has not connected their Google account');
      }

      // Get OAuth token using the Clerk OAuth tokens API
      const tokenResponse = await this.clerkClient.users.getUserOauthAccessToken(userId, 'google');

      if (!tokenResponse || !tokenResponse.data || tokenResponse.data.length === 0) {
        this.logger.error('Failed to get OAuth token', { userId });
        throw new Error('Failed to get OAuth token from Clerk');
      }

      // Clerk v6.x returns a response with data array containing tokens
      const token = tokenResponse.data[0]?.token;

      if (!token) {
        this.logger.error('Token is missing in response', { userId });
        throw new Error('Invalid token format from Clerk');
      }

      // Get scopes from verification
      const scopes = googleAccount.verification?.scopes || [];

      // Create OAuth2 client with the token
      // Note: No client_id/client_secret needed because we're using pre-authorized tokens from Clerk
      const oauth2Client = new google.auth.OAuth2();

      // Handle token format - Clerk returns either a string or an object
      if (typeof token === 'string') {
        oauth2Client.setCredentials({
          access_token: token,
          token_type: 'Bearer',
          scope: scopes.join(' ')
        });
      } else if (typeof token === 'object') {
        oauth2Client.setCredentials({
          access_token: token.access_token,
          token_type: token.token_type || 'Bearer',
          scope: scopes.join(' ')
        });
      } else {
        throw new Error(`Unexpected token format: ${typeof token}`);
      }

      const gmailService = new GmailService(oauth2Client, this.logger);
      await gmailService.initialize();

      this.logger.info('Gmail service initialized successfully', {
        userId,
        monitoredEmail: gmailService.getMonitoredEmail()
      });

      return gmailService;
    } catch (error) {
      this.logger.error('Failed to get Gmail client', {
        userId,
        error: error instanceof Error ? error.message : String(error)
      });
      throw new Error('Gmail API authentication failed: ' + (error instanceof Error ? error.message : String(error)));
    }
  }

  async getGmailClientForEmail(email: string): Promise<GmailService> {
    try {
      const userId = await this.getClerkUserIdByEmail(email);
      return this.getGmailClientForUser(userId);
    } catch (error) {
      this.logger.error('Failed to get Gmail client by email', {
        email,
        error: error instanceof Error ? error.message : String(error)
      });
      throw new Error(`Gmail access failed for ${email}`);
    }
  }

  async getUserEmails(userId: string): Promise<UserEmail[]> {
    try {
      const user = await this.clerkClient.users.getUser(userId);
      return user.emailAddresses
        .filter((e: any) => e.verification?.status === 'verified')
        .map((e: any) => ({
          address: e.emailAddress,
          verified: true,
          primary: e.id === user.primaryEmailAddressId
        }));
    } catch (error) {
      this.logger.error('Failed to get user emails', {
        userId,
        error: error instanceof Error ? error.message : String(error)
      });
      throw new Error('Could not retrieve user email information');
    }
  }
}
