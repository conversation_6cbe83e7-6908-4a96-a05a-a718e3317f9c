FROM node:18-alpine

WORKDIR /app

# Copy package files first for better caching
COPY package*.json ./

# Install dependencies
RUN npm install

# Install ts-node for development
RUN npm install -g ts-node

# Accept build argument for NODE_ENV
ARG NODE_ENV=development
ENV NODE_ENV=${NODE_ENV}

# For development mode, we don't need to copy the code or build
# as we'll mount the code as a volume. For production, we copy and build.
COPY . .

# Only build in production mode
RUN if [ "$NODE_ENV" = "production" ]; then npm run build; fi

# Expose the port the app runs on
EXPOSE 3000

# Use different commands based on environment
CMD if [ "$NODE_ENV" = "production" ]; then npm start; else npm run dev; fi