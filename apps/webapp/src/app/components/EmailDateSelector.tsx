'use client'

import { useState, useEffect, useRef } from 'react'
import { Card } from './ui'
import { useTokens } from '../TokenContext'
import { useDateRange } from '../DateRangeContext'
import { PollingEmailAnalysisProgress, PollingEmailAnalysisProgressRef } from './PollingEmailAnalysisProgress'

interface AnalysisResults {
  remainingTokens: number;
  success: number;
  failed: number;
  errors: Array<{
    emailId: string;
    error: string;
  }>;
  processedEmails: Array<{
    id: string;
    analysis: any;
  }>;
  message?: string;
}

interface TokenEstimate {
  estimatedTokens: number;
  totalEmails: number;
  hasEnoughTokens: boolean;
  remainingTokens: number;
  error?: string;
}

export function EmailDateSelector() {
  const { updateTokens, refreshTokens } = useTokens()
  const { setDateRange } = useDateRange()
  const [startDate, setStartDate] = useState('')
  const [endDate, setEndDate] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [estimate, setEstimate] = useState<TokenEstimate | null>(null)
  const [fetchingEstimate, setFetchingEstimate] = useState(false)
  const [errorMsg, setErrorMsg] = useState<string | null>(null)

  const progressRef = useRef<PollingEmailAnalysisProgressRef>(null)

  // Fetch token estimate when dates change
  const fetchTokenEstimate = async () => {
    if (!startDate || !endDate) {
      setEstimate(null)
      setErrorMsg(null)
      return
    }

    try {
      setFetchingEstimate(true)
      setErrorMsg(null)

      console.log('Fetching estimate for dates:', { startDate, endDate })

      const response = await fetch('/api/emails/estimate', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ startDate, endDate }),
      })

      const data = await response.json()
      console.log('Received estimate:', data)

      if (!response.ok) {
        throw new Error(data.message || 'Failed to fetch estimate')
      }

      // Update the token context when we get a fresh token count
      if (typeof data.remainingTokens === 'number') {
        updateTokens(data.remainingTokens)
      }

      setEstimate(data)

      // Check if we got zero emails but no error
      if (data.totalEmails === 0 && !data.error) {
        setErrorMsg('No emails found in this date range. Try selecting a different period.')
      } else if (data.error) {
        setErrorMsg(data.error)
      }
    } catch (error) {
      console.error('Failed to fetch token estimate:', error)
      setErrorMsg(error instanceof Error ? error.message : 'Failed to fetch estimate')
    } finally {
      setFetchingEstimate(false)
    }
  }

  // Handle the analyze button click
  const handleAnalyze = async () => {
    if (!startDate || !endDate) {
      alert('Please select both start and end dates')
      return
    }

    if (!progressRef.current) {
      console.error('EmailDateSelector: Progress component ref is not available')
      setErrorMsg('Progress component not ready. Please try refreshing the page.')
      return
    }

    console.log('EmailDateSelector: Starting analysis with date range', { startDate, endDate })
    setIsLoading(true)
    setErrorMsg(null)

    try {
      // Use the new startAnalysis method which handles both connection and analysis
      await progressRef.current.startAnalysis(startDate, endDate)
      console.log('EmailDateSelector: Analysis started successfully')
    } catch (error) {
      console.error('EmailDateSelector: Failed to start analysis:', error)
      setErrorMsg(`Failed to start analysis: ${error instanceof Error ? error.message : String(error)}`)
    } finally {
      setIsLoading(false)
    }
  }

  // Update token estimate and date range context when dates change
  useEffect(() => {
    if (startDate && endDate) {
      fetchTokenEstimate()
      setDateRange(startDate, endDate)
    }
  }, [startDate, endDate, setDateRange])

  return (
    <Card className="p-6">
      <h2 className="text-xl font-semibold mb-4">Analyze New Emails</h2>
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <label htmlFor="startDate" className="block text-sm font-medium">
              Start Date
            </label>
            <input
              type="date"
              id="startDate"
              value={startDate}
              onChange={(e) => setStartDate(e.target.value)}
              className="w-full px-3 py-2 border rounded-md bg-background"
              max={endDate || undefined}
            />
          </div>
          <div className="space-y-2">
            <label htmlFor="endDate" className="block text-sm font-medium">
              End Date
            </label>
            <input
              type="date"
              id="endDate"
              value={endDate}
              onChange={(e) => setEndDate(e.target.value)}
              className="w-full px-3 py-2 border rounded-md bg-background"
              min={startDate || undefined}
              max={new Date().toISOString().split('T')[0]}
            />
          </div>
        </div>

        {errorMsg && (
          <div className="p-3 border rounded-md bg-destructive/10 text-destructive">
            <p>{errorMsg}</p>
          </div>
        )}

        {fetchingEstimate && (
          <div className="p-4 border rounded-md bg-secondary/30">
            <p className="text-sm">Calculating token estimate...</p>
          </div>
        )}

        {estimate && !fetchingEstimate && !errorMsg && (
          <div className="p-4 border rounded-md bg-secondary/30">
            <h3 className="text-md font-semibold mb-2">Analysis Requirements</h3>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <div className="text-sm text-muted-foreground">Total Emails Found:</div>
                <div className="text-lg font-semibold">{estimate.totalEmails.toLocaleString()}</div>
              </div>
              <div>
                <div className="text-sm text-muted-foreground">Tokens Required:</div>
                <div className="text-lg font-semibold">{estimate.estimatedTokens.toLocaleString()}</div>
              </div>
            </div>

            {!estimate.hasEnoughTokens && (
              <div className="mt-3 p-2 bg-destructive/10 border border-destructive rounded-md">
                <div className="text-destructive font-medium">
                  ⚠️ You don't have enough tokens for this analysis. Please select a smaller date range.
                </div>
              </div>
            )}

            {estimate.hasEnoughTokens && estimate.totalEmails > 0 && (
              <div className="mt-3 p-2 bg-green-100 dark:bg-green-900/20 border border-green-500 rounded-md">
                <div className="text-green-700 dark:text-green-400 font-medium">
                  ✓ You have enough tokens for this analysis. {estimate.remainingTokens - estimate.estimatedTokens} tokens will remain after analysis.
                </div>
              </div>
            )}
          </div>
        )}

        <PollingEmailAnalysisProgress
          ref={progressRef}
          onAnalysisComplete={(results) => {
            console.log('Analysis completed:', results)
            // Refresh tokens and date range
            refreshTokens()
            setDateRange(startDate, endDate)
          }}
          onAnalysisError={(error) => {
            console.error('Analysis error:', error)
            setErrorMsg(error)
          }}
        />

        <div className="flex justify-end">
          <button
            onClick={handleAnalyze}
            disabled={
              isLoading ||
              fetchingEstimate ||
              !startDate ||
              !endDate ||
              !!errorMsg ||
              (estimate ? (!estimate.hasEnoughTokens || estimate.totalEmails === 0) : false)
            }
            className="px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isLoading ? 'Analyzing...' : 'Analyze Emails'}
          </button>
        </div>
      </div>
    </Card>
  )
}