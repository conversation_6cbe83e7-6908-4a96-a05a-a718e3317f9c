'use client'

import { useEffect, useState, forwardRef, useImperativeHandle } from 'react'
import { io, Socket } from 'socket.io-client'
import { useAuth } from '@clerk/nextjs'
import { useTokens } from '../TokenContext'

// Add global declarations for TypeScript
declare global {
  interface Window {
    _lastAnalysisStartTime?: number;
    _analysisResetTimeout?: NodeJS.Timeout;
  }
}

// Define the progress state interface
interface ProgressState {
  total: number
  processed: number
  current: string
  status: 'idle' | 'processing' | 'completed' | 'error'
  cachedCount: number
  newAnalysisCount?: number
  remainingTokens?: number
  error?: string
  estimatedTotal?: number // Add estimated total for reconciling with the UI
  lastUpdated?: number // Timestamp of the last update
}

// Define the component handle interface exposed via ref
export interface EmailAnalysisProgressHandles {
  // New method that does both connection management and analysis triggering
  startAnalysis: (startDate: string, endDate: string) => Promise<void>
  // Keep isConnected for compatibility during transition
  isConnected: () => boolean
}

export const SocketEmailAnalysisProgress = forwardRef<EmailAnalysisProgressHandles, {}>((props, ref) => {
  // Default progress state
  const [progress, setProgress] = useState<ProgressState>({
    total: 0,
    processed: 0,
    current: 'Waiting for connection...',
    status: 'idle',
    cachedCount: 0,
    newAnalysisCount: 0,
    estimatedTotal: 0
  })

  const [socket, setSocket] = useState<Socket | null>(null)
  const [connected, setConnected] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [analysisInProgress, setAnalysisInProgress] = useState(false)
  const [lastKnownGoodState, setLastKnownGoodState] = useState<ProgressState | null>(null)

  // Get Clerk auth information
  const { getToken } = useAuth()

  // Get token utilities
  const { updateTokens, refreshTokens } = useTokens()

  // Set up Socket.IO connection
  useEffect(() => {
    const setupSocket = async () => {
      try {
        console.log('SocketEmailAnalysisProgress: Setting up socket connection')
        const token = await getToken()

        if (!token) {
          console.error('SocketEmailAnalysisProgress: No auth token available')
          setError('Authentication error. Please try refreshing the page.')
          return
        }

        // If we're reconnecting and have a last known good state, make sure we don't lose it
        if (!connected && lastKnownGoodState) {
          console.log('SocketEmailAnalysisProgress: Reconnecting with last known good state:', lastKnownGoodState)
          // Keep the current progress state during reconnection
          setProgress(prev => ({
            ...prev,
            current: prev.current + ' (reconnecting...)'
          }))
        }

        // Create socket instance with auth token
        // We'll include the token both in auth and query params for maximum compatibility
        const socketInstance = io({
          path: '/api/socket',
          auth: { token },
          query: { token }, // Include token in query params as fallback
          reconnectionAttempts: 5,
          reconnectionDelay: 1000,
          reconnectionDelayMax: 5000,
          timeout: 20000
        })

        // Set up event handlers
        socketInstance.on('connect', () => {
          console.log('SocketEmailAnalysisProgress: Socket connected')
          setConnected(true)
          setError(null)
        })

        socketInstance.on('disconnect', () => {
          console.log('SocketEmailAnalysisProgress: Socket disconnected')
          setConnected(false)

          // If we have progress and analysis is in progress, update the UI to show disconnection
          // but preserve the progress state
          if (analysisInProgress || progress.processed > 0) {
            console.log('SocketEmailAnalysisProgress: Preserving progress state during disconnection')
            setProgress(prev => ({
              ...prev,
              current: prev.current + ' (connection lost, reconnecting...)'
            }))

            // Save the current state as the last known good state if we don't have one yet
            if (!lastKnownGoodState) {
              setLastKnownGoodState(progress)
            }
          }
        })

        socketInstance.on('connect_error', (err) => {
          console.error('SocketEmailAnalysisProgress: Socket connection error:', err)
          setConnected(false)

          // Check if this is a production environment where Socket.IO is disabled
          if (err.message.includes('server error') || err.message.includes('404')) {
            console.log('SocketEmailAnalysisProgress: Socket.IO server not available (production mode), disabling real-time updates')
            setError(null) // Don't show error for expected production behavior
            return
          }

          // Only show error if we're not in a completed state
          // This prevents showing connection errors after analysis is complete
          if (progress.status !== 'completed') {
            setError(`Connection error: ${err.message}`)
          }

          // Log more details about the error
          if ((err as any).data) {
            console.error('SocketEmailAnalysisProgress: Additional error data:', (err as any).data)
          }

          // Try to reconnect with a new token after a delay
          setTimeout(async () => {
            try {
              const freshToken = await getToken()
              if (freshToken && socketInstance) {
                console.log('SocketEmailAnalysisProgress: Attempting reconnection with fresh token')
                socketInstance.auth = { token: freshToken }
                socketInstance.io.opts.query = { token: freshToken }
                socketInstance.connect()
              }
            } catch (tokenError) {
              console.error('SocketEmailAnalysisProgress: Error getting fresh token:', tokenError)
            }
          }, 5000) // Wait 5 seconds before trying to reconnect
        })

        // Handle progress updates
        socketInstance.on('progress', (data) => {
          console.log('SocketEmailAnalysisProgress: Progress update received:', data)

          // Log the current analysis state
          console.log('SocketEmailAnalysisProgress: Current analysis state:', {
            analysisInProgress,
            currentStatus: data.status,
            socketConnected: connected,
            hasSocket: !!socket
          })

          // Check if this is a reconnection message with no real progress data
          const isReconnectionMessage = data.status === 'idle' &&
                                      data.processed === 0 &&
                                      data.total === 0 &&
                                      data.current === 'Connected successfully. Ready for analysis.';

          // If this is a reconnection message and we have a last known good state, use that instead
          if (isReconnectionMessage && lastKnownGoodState &&
              (lastKnownGoodState.processed > 0 || lastKnownGoodState.status === 'processing' || analysisInProgress)) {
            console.log('SocketEmailAnalysisProgress: Detected reconnection message, preserving last known state:', lastKnownGoodState);

            // If analysis is in progress, make sure we keep the processing status
            if (analysisInProgress) {
              setProgress({
                ...lastKnownGoodState,
                status: 'processing',
                current: lastKnownGoodState.current + ' (reconnected)'
              });
            }
            return;
          }

          // Force the status to 'processing' if we're receiving updates and analysis is in progress
          // This ensures the UI shows the progress bar
          if (analysisInProgress && data.status === 'idle') {
            console.log('SocketEmailAnalysisProgress: Forcing status to processing because analysis is in progress')
            data.status = 'processing'
          }

          // Update the progress state with the new data
          setProgress(prevProgress => {
            console.log('SocketEmailAnalysisProgress: Updating progress state:', {
              prevStatus: prevProgress.status,
              newStatus: data.status,
              prevProcessed: prevProgress.processed,
              newProcessed: data.processed,
              prevTotal: prevProgress.total,
              newTotal: data.total,
              dataObject: JSON.stringify(data)
            })

            // If we're getting an idle status but we're already processing,
            // keep the processing status to avoid UI flickering
            if (prevProgress.status === 'processing' && data.status === 'idle') {
              console.log('SocketEmailAnalysisProgress: Preserving processing status')
              return {
                ...prevProgress,
                ...data,
                status: 'processing'
              }
            }

            // Make sure we preserve the total if it's not provided in the new data
            // but we already have a value
            const newTotal = data.total || prevProgress.total;
            const newProcessed = data.processed || prevProgress.processed;

            // Check if we have an estimatedTotal that differs from the total
            // This helps reconcile the difference between the estimate and actual processing count
            const estimatedTotal = data.estimatedTotal || prevProgress.estimatedTotal || newTotal;

            // Create the new state
            const newState = {
              ...prevProgress,
              ...data,
              // Ensure these values are always set
              total: newTotal,
              processed: newProcessed,
              estimatedTotal,
              lastUpdated: Date.now()
            };

            // Log the final state we're setting
            console.log('SocketEmailAnalysisProgress: Final progress state:', {
              status: newState.status,
              processed: newState.processed,
              total: newState.total,
              estimatedTotal: newState.estimatedTotal,
              current: newState.current,
              cachedCount: newState.cachedCount
            });

            // If this is a meaningful update (not just a connection message),
            // save it as the last known good state
            if (newState.processed > 0 || newState.status === 'processing' || newState.status === 'completed') {
              setLastKnownGoodState(newState);
            }

            return newState;
          })

          // Clear the analysis reset timeout since we received a progress update
          if (window._analysisResetTimeout) {
            clearTimeout(window._analysisResetTimeout)
            window._analysisResetTimeout = undefined
          }

          // Update token information if available
          if (typeof data.remainingTokens === 'number') {
            updateTokens(data.remainingTokens)
          }

          // If we get a completed or error status, mark analysis as no longer in progress
          if (data.status === 'completed' || data.status === 'error') {
            console.log(`SocketEmailAnalysisProgress: Process ${data.status}, updating state`)
            setAnalysisInProgress(false)

            // Refresh tokens one more time when complete
            refreshTokens()
          } else if (data.status === 'processing') {
            // If we get a processing status, make sure analysisInProgress is true
            if (!analysisInProgress) {
              console.log('SocketEmailAnalysisProgress: Setting analysisInProgress to true based on processing status')
              setAnalysisInProgress(true)
            }
          }
        })

        // Force reset analysis state after a timeout (safety mechanism)
        const resetAnalysisTimeout = setTimeout(() => {
          if (analysisInProgress) {
            console.warn('SocketEmailAnalysisProgress: Forcing reset of analysis state after timeout')
            setAnalysisInProgress(false)
            setProgress(prev => ({
              ...prev,
              status: 'error',
              current: 'Analysis timed out. Please try again.'
            }))
          }
        }, 300000) // 5 minutes timeout

        setSocket(socketInstance)

        return () => {
          console.log('SocketEmailAnalysisProgress: Cleaning up socket connection')
          clearTimeout(resetAnalysisTimeout)
          socketInstance.disconnect()
        }
      } catch (error) {
        console.error('SocketEmailAnalysisProgress: Error setting up socket:', error)
        setError(`Failed to connect: ${error instanceof Error ? error.message : String(error)}`)
      }
    }

    setupSocket()

    // Reset analysis state when component mounts
    setAnalysisInProgress(false)
  }, [getToken, updateTokens, refreshTokens, analysisInProgress])

  // Expose methods via ref
  useImperativeHandle(ref, () => ({
    // For backward compatibility during transition
    isConnected: () => connected,

    // New streamlined method that makes the API call to trigger analysis
    startAnalysis: async (startDate: string, endDate: string) => {
      console.log('SocketEmailAnalysisProgress: startAnalysis called', { startDate, endDate })

      // Force reset analysis state if it's been more than 5 minutes since the last analysis started
      const currentTime = Date.now()
      const lastAnalysisTime = window._lastAnalysisStartTime || 0
      const timeSinceLastAnalysis = currentTime - lastAnalysisTime

      if (analysisInProgress && timeSinceLastAnalysis > 300000) { // 5 minutes
        console.warn('SocketEmailAnalysisProgress: Forcing reset of stale analysis state')
        setAnalysisInProgress(false)
      }

      if (analysisInProgress) {
        console.warn('SocketEmailAnalysisProgress: Analysis already in progress')
        throw new Error('Analysis already in progress')
      }

      if (!connected || !socket) {
        throw new Error('Socket not connected')
      }

      // Store the analysis start time globally
      window._lastAnalysisStartTime = Date.now()
      setAnalysisInProgress(true)

      try {
        // Completely reset progress for new analysis
        const newProgressState = {
          total: 0,
          processed: 0,
          current: 'Starting analysis...',
          status: 'processing' as const,
          cachedCount: 0,
          newAnalysisCount: 0,
          estimatedTotal: 0, // Reset estimated total too
          lastUpdated: Date.now()
        };

        setProgress(newProgressState);

        // Reset the last known good state when starting a new analysis
        setLastKnownGoodState(newProgressState);

        console.log('SocketEmailAnalysisProgress: Completely reset progress state for new analysis')

        // Make API call to start analysis
        const response = await fetch('/api/emails/analyze', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ startDate, endDate }),
        })

        const results = await response.json()

        // Update token information if available
        if (typeof results.remainingTokens === 'number') {
          updateTokens(results.remainingTokens)
        }

        if (!response.ok) {
          console.error('SocketEmailAnalysisProgress: Analysis API call failed', results)
          setProgress(prev => ({
            ...prev,
            status: 'error',
            current: results.message || 'Analysis request failed'
          }))
          setAnalysisInProgress(false)
          throw new Error(results.message || 'Analysis failed')
        }

        console.log('SocketEmailAnalysisProgress: Analysis successfully initiated', results)

        // Set a backup timeout to reset the analysis state if no progress updates are received
        const resetTimeout = setTimeout(() => {
          if (analysisInProgress) {
            console.warn('SocketEmailAnalysisProgress: No progress updates received, resetting analysis state')
            setAnalysisInProgress(false)
            setProgress(prev => ({
              ...prev,
              status: 'error',
              current: 'Analysis timed out. No progress updates received.'
            }))
          }
        }, 60000) // 1 minute timeout

        // Store the timeout ID so we can clear it if progress updates are received
        window._analysisResetTimeout = resetTimeout

        return
      } catch (error) {
        setAnalysisInProgress(false)
        console.error('SocketEmailAnalysisProgress: Error starting analysis:', error)
        setProgress(prev => ({
          ...prev,
          status: 'error',
          current: `Error: ${error instanceof Error ? error.message : String(error)}`
        }))
        throw error
      }
    }
  }))

  // Render the component UI
  return (
    <div className="mb-8 p-6 bg-card rounded-lg shadow border">
      <h2 className="text-2xl font-bold mb-4 text-card-foreground">Email Analysis Progress</h2>

      {error && (
        <div className="mb-4 p-3 bg-red-100 text-red-800 rounded dark:bg-red-900 dark:text-red-200">
          {error}
        </div>
      )}

      <div className="mb-4">
        <div className="flex justify-between mb-2 text-card-foreground">
          <span>Status:</span>
          <span className="font-semibold">
            {progress.status === 'idle' && !analysisInProgress && 'Ready'}
            {(progress.status === 'processing' || analysisInProgress) && 'Processing...'}
            {progress.status === 'completed' && 'Completed'}
            {progress.status === 'error' && 'Error'}
          </span>
        </div>

        {/* Show progress bar if status is processing OR analysis is in progress */}
        {(progress.status === 'processing' || analysisInProgress) && (
          <div className="w-full bg-muted rounded-full h-4 mb-2">
            <div
              className="bg-primary h-4 rounded-full transition-all duration-500"
              style={{
                width: `${progress.total && progress.processed ?
                  Math.min(100, Math.max(5, (progress.processed / progress.total) * 100)) : 5}%`
              }}
            ></div>
          </div>
        )}

        {/* Display both estimated total and processing total if they differ */}
        {progress.estimatedTotal && progress.estimatedTotal !== progress.total && (
          <div className="text-xs text-muted-foreground mb-2">
            <span>Found {progress.estimatedTotal} emails in date range, processing {progress.total} emails</span>
          </div>
        )}

        {/* Show progress text if status is processing OR analysis is in progress */}
        {(progress.status === 'processing' || analysisInProgress) && (
          <div className="text-sm text-card-foreground mb-2">
            {progress.total > 0 && progress.processed > 0 ? (
              <>
                Processed {progress.processed} of {progress.total} emails
                {progress.cachedCount > 0 && ` (${progress.cachedCount} from cache)`}
              </>
            ) : (
              'Preparing analysis...'
            )}
          </div>
        )}

        {/* Debug information about progress state */}
        <div className="text-xs text-muted-foreground mt-1 mb-2">
          Progress: {progress.processed}/{progress.total} - Status: {progress.status}
          {progress.estimatedTotal && progress.estimatedTotal !== progress.total &&
            ` - Estimated Total: ${progress.estimatedTotal}`}
        </div>

        <div className="text-sm text-card-foreground">{progress.current}</div>

        {/* Debug info */}
        <div className="text-xs text-muted-foreground mt-2">
          Internal state: {analysisInProgress ? 'Analysis in progress' : 'No analysis in progress'}
        </div>
      </div>

      <div className="flex items-center gap-2 text-xs mt-4">
        <div className={`w-2 h-2 rounded-full ${connected ? 'bg-green-500' : 'bg-yellow-500 animate-pulse'}`}></div>
        <div className={`text-muted-foreground ${!connected && 'italic'}`}>
          {connected
            ? `Connected via secure WebSocket (${socket ? 'active' : 'inactive'})`
            : (lastKnownGoodState && lastKnownGoodState.processed > 0)
              ? 'Reconnecting... (progress preserved)'
              : 'Connecting to server...'}
        </div>
      </div>

      {/* Show reconnection status when appropriate */}
      {!connected && lastKnownGoodState && lastKnownGoodState.processed > 0 && (
        <div className="text-xs text-yellow-500 mt-2">
          Connection lost. Your progress is saved and will continue when reconnected.
        </div>
      )}

      {/* Only show this message when we're completed but not connected */}
      {progress.status === 'completed' && !connected && (
        <div className="text-xs text-green-500 mt-2">
          Analysis is complete. Socket reconnection in progress...
        </div>
      )}
    </div>
  )
})

// Add display name for better debugging
SocketEmailAnalysisProgress.displayName = 'SocketEmailAnalysisProgress'
