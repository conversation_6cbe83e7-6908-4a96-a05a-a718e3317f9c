'use client'

import { useState, useEffect, useRef, useMemo } from 'react'
import { Card, Select, LineChart } from './ui'
import { useAuth } from '@clerk/nextjs'
import { useSocket } from '../SocketContext'
import { useDateRange } from '../DateRangeContext'

type TimeRange = 'daily' | 'weekly' | 'monthly'
type MetricType = 'applications' | 'interviews'

// Define chart colors
const CHART_COLORS = {
  applications: '#8b5cf6', // Purple
  interviews: '#2563eb',   // Blue
}

interface MetricsData {
  applications: {
    [key in TimeRange]?: Array<{ date: string; value: number }>
  }
  interviews: {
    [key in TimeRange]?: Array<{ date: string; value: number }>
  }
}

export function MetricsOverview() {
  const [timeRange, setTimeRange] = useState<TimeRange>('weekly')
  const [selectedMetrics, setSelectedMetrics] = useState<MetricType[]>(['applications'])
  const [data, setData] = useState<MetricsData>({
    applications: { daily: [], weekly: [], monthly: [] },
    interviews: { daily: [], weekly: [], monthly: [] }
  })
  const [isLoading, setIsLoading] = useState<boolean>(true)
  const [error, setError] = useState<string | null>(null)
  const { isLoaded, isSignedIn } = useAuth()
  const { socket } = useSocket()
  const { startDate, endDate } = useDateRange()
  const refreshTimeoutRef = useRef<NodeJS.Timeout | null>(null)

  // Options for the multi-select
  const metricOptions = [
    { label: 'Applications', value: 'applications' },
    { label: 'Interviews', value: 'interviews' }
  ]

  // Use date range from context, or default to last 3 months if not set
  const effectiveEndDate = endDate || new Date().toISOString().split('T')[0]
  const effectiveStartDate = startDate || new Date(Date.now() - 90 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]

  // Function to fetch metrics data
  const fetchMetrics = async () => {
    console.log('MetricsOverview: Fetching metrics data', { effectiveStartDate, effectiveEndDate, timeRange })
    setIsLoading(true)
    setError(null)

    try {
      // Construct the API URL with query parameters
      const url = `/api/metrics?startDate=${effectiveStartDate}&endDate=${effectiveEndDate}&period=${timeRange}`
      console.log('MetricsOverview: Fetching from URL', url)
      const response = await fetch(url)

      if (!response.ok) {
        console.error('MetricsOverview: API response not OK', { status: response.status, statusText: response.statusText })
        throw new Error(`Failed to fetch metrics: ${response.statusText}`)
      }

      const metricsData = await response.json()
      console.log('MetricsOverview: Received metrics data', metricsData)

      // Check if we have data for the selected metrics
      selectedMetrics.forEach(metric => {
        if (!metricsData[metric][timeRange] || metricsData[metric][timeRange].length === 0) {
          console.warn(`No ${metric} data available for ${timeRange} period`)
        }
      })

      setData(metricsData)
    } catch (err) {
      console.error('MetricsOverview: Error fetching metrics:', err)
      setError(err instanceof Error ? err.message : 'Failed to fetch metrics data')
    } finally {
      setIsLoading(false)
    }
  }

  // Fetch metrics data when component mounts or parameters change
  useEffect(() => {
    if (!isLoaded || !isSignedIn) return
    fetchMetrics()
  }, [isLoaded, isSignedIn, timeRange, effectiveStartDate, effectiveEndDate])

  // Prepare chart data and series
  const { chartData, chartSeries } = useMemo(() => {
    // If no metrics are selected or no data is available, return empty values
    if (selectedMetrics.length === 0 || !data || isLoading) {
      return { chartData: [], chartSeries: [] };
    }

    // Get the data for the current time range
    const currentData = data[selectedMetrics[0]][timeRange] || [];

    // If there's no data, return empty values
    if (currentData.length === 0) {
      return { chartData: [], chartSeries: [] };
    }

    // Create a map of dates to data points
    const dateMap = new Map<string, any>();

    // Initialize the map with all dates from the first metric
    currentData.forEach(item => {
      dateMap.set(item.date, { date: item.date });
    });

    // Add data for each selected metric
    selectedMetrics.forEach(metric => {
      const metricData = data[metric][timeRange] || [];
      metricData.forEach(item => {
        const existingItem = dateMap.get(item.date);
        if (existingItem) {
          existingItem[metric] = item.value;
        } else {
          const newItem: any = { date: item.date };
          newItem[metric] = item.value;
          dateMap.set(item.date, newItem);
        }
      });
    });

    // Convert the map to an array and sort by date
    const chartData = Array.from(dateMap.values()).sort((a, b) => {
      return a.date.localeCompare(b.date);
    });

    // Create series configuration for each selected metric
    const chartSeries = selectedMetrics.map(metric => ({
      name: metric === 'applications' ? 'Applications' : 'Interviews',
      dataKey: metric,
      color: CHART_COLORS[metric]
    }));

    return { chartData, chartSeries };
  }, [data, selectedMetrics, timeRange, isLoading]);

  // Listen for socket events to refresh metrics during analysis
  useEffect(() => {
    if (!socket || !isLoaded || !isSignedIn) {
      console.log('MetricsOverview: Socket effect not running', { hasSocket: !!socket, isLoaded, isSignedIn })
      return
    }

    console.log('MetricsOverview: Setting up socket listener for progress updates')

    const handleProgressUpdate = (data: any) => {
      console.log('MetricsOverview: Received progress update', data)

      // Debounce the refresh to avoid too many API calls
      if (refreshTimeoutRef.current) {
        console.log('MetricsOverview: Clearing existing refresh timeout')
        clearTimeout(refreshTimeoutRef.current)
      }

      // Only refresh every 5 seconds at most during processing
      if (data.status === 'processing' && data.processed > 0) {
        console.log('MetricsOverview: Setting refresh timeout for metrics data')
        refreshTimeoutRef.current = setTimeout(() => {
          console.log('MetricsOverview: Refreshing metrics data due to progress update')
          fetchMetrics()
        }, 5000)
      }

      // Always refresh on completion
      if (data.status === 'completed') {
        console.log('MetricsOverview: Analysis completed, refreshing metrics')
        fetchMetrics()
      }
    }

    socket.on('progress', handleProgressUpdate)
    console.log('MetricsOverview: Socket listener registered')

    return () => {
      console.log('MetricsOverview: Cleaning up socket listener')
      socket.off('progress', handleProgressUpdate)
      if (refreshTimeoutRef.current) {
        clearTimeout(refreshTimeoutRef.current)
      }
    }
  }, [socket, isLoaded, isSignedIn])

  return (
    <Card className="p-6">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h2 className="text-xl font-semibold">Metrics Overview</h2>
          {effectiveStartDate && effectiveEndDate && (
            <p className="text-sm text-muted-foreground mt-1">
              Date range: {new Date(effectiveStartDate).toLocaleDateString()} - {new Date(effectiveEndDate).toLocaleDateString()}
            </p>
          )}
        </div>
        <div className="flex gap-4">
          <Select
            value=""
            onValueChange={() => {}}
            multiple={true}
            values={selectedMetrics}
            onValuesChange={(values: string[]) => {
              // Ensure at least one metric is selected
              if (values.length > 0) {
                setSelectedMetrics(values as MetricType[])
              }
            }}
            options={metricOptions}
            placeholder="Select metrics"
          />
          <Select
            value={timeRange}
            onValueChange={(value: string) => setTimeRange(value as TimeRange)}
            options={[
              { label: 'Daily', value: 'daily' },
              { label: 'Weekly', value: 'weekly' },
              { label: 'Monthly', value: 'monthly' }
            ]}
          />
        </div>
      </div>

      {isLoading && (
        <div className="flex justify-center items-center h-[400px]">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
        </div>
      )}

      {!isLoading && error && (
        <div className="h-[400px] flex items-center justify-center">
          <div className="text-center p-6 bg-destructive/10 rounded-lg max-w-md">
            <h3 className="text-lg font-medium text-destructive mb-2">Error Loading Metrics</h3>
            <p className="text-sm text-muted-foreground">{error}</p>
            <p className="text-sm mt-4">Try running an email analysis first to generate metrics data.</p>
          </div>
        </div>
      )}

      {!isLoading && !error && chartData.length === 0 && (
        <div className="h-[400px] flex items-center justify-center">
          <div className="text-center p-6 bg-muted/50 rounded-lg max-w-md">
            <h3 className="text-lg font-medium mb-2">No Data Available</h3>
            <p className="text-sm text-muted-foreground">
              No data found for the selected metrics and time period.
            </p>
            <p className="text-sm mt-4">Try running an email analysis first or select a different time range.</p>
          </div>
        </div>
      )}

      {!isLoading && !error && chartData.length > 0 && (
        <div className="h-[400px]">
          <LineChart
            data={chartData}
            xField="date"
            series={chartSeries}
            title="Job Search Metrics Over Time"
          />
        </div>
      )}
    </Card>
  )
}