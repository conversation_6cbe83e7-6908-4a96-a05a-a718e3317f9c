'use client'

import { createContext, useContext, useState, useEffect, ReactNode } from 'react'
import { io, Socket } from 'socket.io-client'
import { useAuth } from '@clerk/nextjs'

interface SocketContextType {
  socket: Socket | null
  connected: boolean
}

const defaultContext: SocketContextType = {
  socket: null,
  connected: false
}

const SocketContext = createContext<SocketContextType>(defaultContext)

export function useSocket() {
  return useContext(SocketContext)
}

interface SocketProviderProps {
  children: ReactNode
}

export function SocketProvider({ children }: SocketProviderProps) {
  const [socket, setSocket] = useState<Socket | null>(null)
  const [connected, setConnected] = useState<boolean>(false)
  const { getToken, isLoaded, isSignedIn } = useAuth()

  useEffect(() => {
    if (!isLoaded || !isSignedIn) return

    const setupSocket = async () => {
      try {
        console.log('SocketContext: Setting up socket connection')
        const token = await getToken()

        if (!token) {
          console.error('SocketContext: No auth token available')
          return
        }

        // Create socket instance with auth token
        const socketInstance = io({
          path: '/api/socket',
          auth: { token },
          query: { token }, // Include token in query params as fallback
          reconnectionAttempts: 5,
          reconnectionDelay: 1000,
          reconnectionDelayMax: 5000,
          timeout: 20000
        })

        // Set up event handlers
        socketInstance.on('connect', () => {
          console.log('SocketContext: Socket connected')
          setConnected(true)
        })

        socketInstance.on('disconnect', () => {
          console.log('SocketContext: Socket disconnected')
          setConnected(false)
        })

        socketInstance.on('connect_error', (error) => {
          console.error('SocketContext: Connection error:', error)
          setConnected(false)

          // Check if this is a production environment where Socket.IO is disabled
          if (error.message.includes('server error') || error.message.includes('404')) {
            console.log('SocketContext: Socket.IO server not available (production mode), disabling socket connection')
            return
          }

          // Try to reconnect with a new token after a delay
          setTimeout(async () => {
            try {
              const freshToken = await getToken()
              if (freshToken && socketInstance) {
                console.log('SocketContext: Attempting reconnection with fresh token')
                socketInstance.auth = { token: freshToken }
                socketInstance.io.opts.query = { token: freshToken }
                socketInstance.connect()
              }
            } catch (tokenError) {
              console.error('SocketContext: Error getting fresh token:', tokenError)
            }
          }, 5000) // Wait 5 seconds before trying to reconnect
        })

        setSocket(socketInstance)

        return () => {
          console.log('SocketContext: Cleaning up socket connection')
          socketInstance.disconnect()
        }
      } catch (error) {
        console.error('SocketContext: Error setting up socket:', error)
      }
    }

    setupSocket()
  }, [getToken, isLoaded, isSignedIn])

  return (
    <SocketContext.Provider
      value={{
        socket,
        connected
      }}
    >
      {children}
    </SocketContext.Provider>
  )
}
