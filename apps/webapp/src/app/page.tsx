import { auth } from '@clerk/nextjs/server'
import { redirect } from 'next/navigation'
import { MetricsOverview } from './components'
import { EmailDateSelector } from './components/EmailDateSelector'

// Force dynamic rendering
export const dynamic = 'force-dynamic'

export default async function Home() {
  const { userId } = await auth()

  if (!userId) {
    redirect('/sign-in')
  }

  return (
    <div className="space-y-8">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold">Dashboard</h1>
      </div>

      <div className="grid gap-6">
        <EmailDateSelector />
        <MetricsOverview />
      </div>
    </div>
  )
}
