import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@clerk/nextjs/server'
const { ServiceContainer } = require('@/services/container')
import { sendProgressUpdate } from '@/lib/progressUpdates'
import { PubSub } from '@google-cloud/pubsub'
import { Database, Logger } from '@webapp/services'
import { v4 as uuidv4 } from 'uuid'

// Mark this route as dynamic to prevent static generation errors
export const dynamic = 'force-dynamic'

// Initialize services
const logger = new Logger()
const database = new Database()

// Define interface for email error
interface EmailError {
  emailId: string;
  error: string;
}

// Define interface for processed email
interface ProcessedEmail {
  id: string;
  analysis: any;
}

// Define the expected response structure from email service
interface EmailServiceResponse {
  success: number;
  failed: number;
  errors: EmailError[];
  processedEmails: ProcessedEmail[];
  cachedCount: number;
  newAnalysisCount: number;
  remainingTokens?: number;
  tokenError?: boolean;
}

interface AnalyzeEmailsRequestBody {
  startDate: string
  endDate: string
}

// Interface for any updates received from the email service
interface EmailServiceProgressUpdate {
  type?: 'progress' | 'complete' | 'error'
  status?: 'idle' | 'processing' | 'completed' | 'error' | 'connecting'
  total?: number
  processed?: number
  currentEmail?: string
  current?: string
  cachedCount?: number
  remainingTokens?: number
  tokenError?: string
}

export async function POST(req: NextRequest) {
  console.log('Analyze Route: POST request received')

  // --- Authentication ---
  const authResult = await auth()
  const userId = authResult.userId

  if (!userId) {
    console.error('Analyze Route: Unauthorized access attempt')
    return NextResponse.json({ message: 'Unauthorized' }, { status: 401 })
  }
  console.log(`Analyze Route: Authenticated user ${userId}`)

  try {
    // --- Parse request body ---
    const body = await req.json() as AnalyzeEmailsRequestBody
    const { startDate, endDate } = body

    if (!startDate || !endDate) {
      console.error('Analyze Route: Missing required fields', body)
      return NextResponse.json({ message: 'Missing required fields' }, { status: 400 })
    }

    console.log(`Analyze Route: Received date range`, { startDate, endDate })

    // --- Initialize services ---
    const container = new ServiceContainer()
    const tokenService = container.getTokenService()
    const authService = container.getAuthService()

    if (!tokenService || !authService) {
      console.error('Analyze Route: Failed to get services', {
        hasTokenService: !!tokenService,
        hasAuthService: !!authService
      })
      return NextResponse.json({ message: 'Service unavailable' }, { status: 500 })
    }

    // --- Check token balance ---
    const userTokens = await tokenService.getRemainingTokens(userId)

    console.log(`Analyze Route: User ${userId} has ${userTokens} tokens available`)

    if (userTokens <= 0) {
      // Tell the client about the token issue through the Socket.IO connection
      sendProgressUpdate(userId, {
        status: 'error',
        current: 'Insufficient tokens to perform analysis.',
        processed: 0,
        total: 0,
        error: 'No tokens available'
      })

      return NextResponse.json(
        { message: 'Insufficient tokens', tokenError: 'No tokens available' },
        { status: 402 } // 402 Payment Required
      )
    }

    // --- Get user's Gmail service to get monitored email ---
    const gmailService = await authService.getGmailClientForUser(userId)
    const monitoredEmail = gmailService.getMonitoredEmail()

    // --- Create job record for tracking ---
    const jobId = uuidv4()
    const jobData = {
      jobId,
      clerkUserId: userId,
      status: 'processing',
      current: 'Starting email analysis...',
      total: 0,
      processed: 0,
      cachedCount: 0,
      newAnalysisCount: 0,
      remainingTokens: userTokens,
      startDate,
      endDate,
      monitoredEmail,
      createdAt: new Date(),
      updatedAt: new Date()
    }

    await database.collection('analysisJobs').doc(jobId).set(jobData)
    logger.info('Created analysis job', { jobId, userId })

    // --- Update client about analysis start (keep Socket.IO for backward compatibility) ---
    sendProgressUpdate(userId, {
      status: 'processing',
      current: 'Starting email analysis...',
      total: 0,
      processed: 0
    })

    // --- Begin analysis process using Cloud Functions ---
    console.log(`Analyze Route: Starting Cloud Function-based analysis for user ${userId}`)

    try {
      // Initialize Pub/Sub client
      const pubsub = new PubSub()

      // Get emails in the date range to trigger analysis for each
      const startDateFormatted = new Date(startDate + 'T00:00:00Z').toISOString().split('T')[0].replace(/-/g, '/')
      const endDateFormatted = new Date(endDate + 'T23:59:59Z').toISOString().split('T')[0].replace(/-/g, '/')
      const emailIds = await gmailService.getEmailsByDateRange(startDateFormatted, endDateFormatted)

      console.log(`Analyze Route: Found ${emailIds.length} emails to analyze`)

      // Update job record with total count
      await database.collection('analysisJobs').doc(jobId).update({
        total: emailIds.length,
        current: `Found ${emailIds.length} emails to analyze`,
        updatedAt: new Date()
      })

      // Update client with total count (keep Socket.IO for backward compatibility)
      sendProgressUpdate(userId, {
        status: 'processing',
        current: `Found ${emailIds.length} emails to analyze`,
        total: emailIds.length,
        processed: 0
      })

      // Trigger Cloud Function for each email
      const analysisPromises = emailIds.map(async (emailId: any, index: number) => {
        const emailAnalysisMessage = {
          clerkUserId: userId,
          messageId: emailId.id,
          monitoredEmail: monitoredEmail,
          jobId: jobId, // Include job ID for tracking
          batchIndex: index,
          totalInBatch: emailIds.length
        }

        await pubsub.topic('email-analysis-requests').publishMessage({
          data: Buffer.from(JSON.stringify(emailAnalysisMessage))
        })

        console.log(`Analyze Route: Published analysis request for email ${emailId.id}`)
      })

      // Wait for all messages to be published
      await Promise.all(analysisPromises)

      console.log(`Analyze Route: Published ${emailIds.length} analysis requests to Cloud Functions`)

      // Send final update indicating that requests have been sent
      sendProgressUpdate(userId, {
        status: 'processing',
        current: `Sent ${emailIds.length} emails for analysis. Processing in Cloud Functions...`,
        total: emailIds.length,
        processed: 0
      })

      // Return immediate success response to client with job ID
      return NextResponse.json({
        message: 'Analysis requests sent to Cloud Functions successfully',
        status: 'processing',
        jobId: jobId,
        totalEmails: emailIds.length
      })

    } catch (cloudFunctionError) {
      console.error(`Analyze Route: Failed to trigger Cloud Functions for user ${userId}`, cloudFunctionError)

      // Send error status
      sendProgressUpdate(userId, {
        status: 'error',
        current: `Error triggering analysis: ${cloudFunctionError instanceof Error ? cloudFunctionError.message : 'Unknown error'}`,
        total: 0,
        processed: 0
      })

      return NextResponse.json({
        message: 'Failed to trigger email analysis',
        error: cloudFunctionError instanceof Error ? cloudFunctionError.message : 'Unknown error'
      }, { status: 500 })
    }

  } catch (error) {
    console.error('Analyze Route: Unexpected error', error)

    // Send error through Socket.IO if possible
    if (userId) {
      sendProgressUpdate(userId, {
        status: 'error',
        current: `Unexpected error: ${error instanceof Error ? error.message : 'Unknown error'}`,
        total: 0,
        processed: 0
      })
    }

    return NextResponse.json(
      { message: `Unexpected error: ${error instanceof Error ? error.message : 'Unknown error'}` },
      { status: 500 }
    )
  }
}
