import { auth } from '@clerk/nextjs/server'
import { NextResponse } from 'next/server'
const { ServiceContainer } = require('@/services/container')

// Mark this route as dynamic to prevent static generation errors
export const dynamic = 'force-dynamic'

export async function GET() {
  try {
    const { userId } = await auth()
    if (!userId) {
      return new NextResponse('Unauthorized', { status: 401 })
    }

    let container, tokenService
    try {
      container = new ServiceContainer()
      tokenService = container.getTokenService()
    } catch (containerError) {
      console.error('Tokens API: Failed to initialize ServiceContainer', containerError)
      // Return mock data if service initialization fails
      return NextResponse.json({
        remaining: 4500,
        total: 5000,
        error: 'Service initialization failed, showing mock data'
      })
    }

    if (!tokenService) {
      console.error('Tokens API: Token service not available')
      // Return mock data if service is not available
      return NextResponse.json({
        remaining: 4500,
        total: 5000,
        error: 'Token service unavailable, showing mock data'
      })
    }

    try {
      // Add debugging information
      console.log('Tokens API: Getting tokens for user:', userId)

      // Try to get remaining tokens for the user
      const remainingTokens = await tokenService.getRemainingTokens(userId)

      console.log('Tokens API: Retrieved tokens:', { userId, remainingTokens })

      return NextResponse.json({
        remaining: remainingTokens,
        total: 5000 // This is hardcoded in the TokenService
      })
    } catch (error) {
      console.error('Error accessing token service, returning mock data:', error)
      // Fall back to mock data if the service call fails
      return NextResponse.json({
        remaining: 4500,
        total: 5000
      })
    }
  } catch (error) {
    console.error('Failed to fetch token counts:', error)
    return new NextResponse('Internal Server Error', { status: 500 })
  }
}