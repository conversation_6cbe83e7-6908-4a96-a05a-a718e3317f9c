import { NextRequest, NextResponse } from 'next/server'

// This route handles Socket.io requests
// The actual Socket.io server is initialized in server.js
export async function GET(request: NextRequest) {
  // Socket.io requests should be handled by the custom server
  // This route exists to prevent 404 errors and provide information
  return NextResponse.json({
    message: 'Socket.io server endpoint',
    status: 'Socket.io should be handled by custom server',
    path: '/api/socket',
    note: 'If you see this message, the Socket.io server may not be properly initialized'
  })
}

export async function POST(request: NextRequest) {
  // Handle Socket.io POST requests
  return NextResponse.json({
    message: 'Socket.io server endpoint',
    status: 'Socket.io should be handled by custom server',
    path: '/api/socket',
    note: 'If you see this message, the Socket.io server may not be properly initialized'
  })
}
