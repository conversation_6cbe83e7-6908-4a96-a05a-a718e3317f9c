import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { Database, Logger } from '@webapp/services';

const logger = new Logger();
const database = new Database();

export async function GET(
  request: NextRequest,
  { params }: { params: { jobId: string } }
) {
  try {
    // Verify authentication
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { jobId } = params;

    // Get job status from database
    const jobDoc = await database.collection('analysisJobs').doc(jobId).get();

    if (!jobDoc.exists) {
      return NextResponse.json({ error: 'Job not found' }, { status: 404 });
    }

    const jobData = jobDoc.data();

    // Verify the job belongs to the authenticated user
    if (jobData?.clerkUserId !== userId) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Return job status
    return NextResponse.json({
      jobId,
      status: jobData.status || 'unknown',
      progress: {
        total: jobData.total || 0,
        processed: jobData.processed || 0,
        current: jobData.current || '',
        cachedCount: jobData.cachedCount || 0,
        newAnalysisCount: jobData.newAnalysisCount || 0,
        remainingTokens: jobData.remainingTokens,
        error: jobData.error
      },
      result: jobData.result,
      createdAt: jobData.createdAt,
      updatedAt: jobData.updatedAt,
      completedAt: jobData.completedAt
    });

  } catch (error) {
    logger.error('Error fetching job status', {
      error: error instanceof Error ? error.message : String(error),
      jobId: params.jobId
    });

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
