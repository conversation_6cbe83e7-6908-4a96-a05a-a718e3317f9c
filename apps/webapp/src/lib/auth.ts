import { auth } from '@clerk/nextjs/server';
import { verifyToken as clerkVerifyToken } from '@clerk/backend';

/**
 * Verify a JWT token from <PERSON> and return the userId if valid
 * @param token JWT token from Clerk
 * @returns userId if token is valid, null otherwise
 */
export async function verifyToken(token: string): Promise<string | null> {
  try {
    if (!token) {
      console.error('Token verification failed: No token provided');
      return null;
    }

    // For development mode, we can either:
    // 1. Use simplified verification (original approach)
    // 2. Use proper verification (more secure but requires correct setup)
    if (process.env.NODE_ENV === 'development' && process.env.BYPASS_TOKEN_VERIFICATION === 'true') {
      console.log('Development mode: Accepting token without full verification');
      return 'user_from_token';
    }

    try {
      // Use Clerk's verifyToken function from @clerk/backend
      const verified = await clerkVerifyToken(token, {
        secretKey: process.env.CLERK_SECRET_KEY,
        // You can also use jwtKey for networkless verification if available
        // jwtKey: process.env.CLERK_JWT_KEY,
      });

      // The subject claim contains the user ID
      const userId = verified.sub;

      if (!userId) {
        console.error('Token verification failed: No subject in token');
        return null;
      }

      return userId;
    } catch (verifyError) {
      console.error('Error verifying token with Clerk:', verifyError);
      return null;
    }
  } catch (error) {
    console.error('Token verification error:', error);
    return null;
  }
}

/**
 * Get user information from Clerk by userId
 * @param userId Clerk user ID
 * @returns User object if found, null otherwise
 */
export async function getUserById(userId: string) {
  try {
    // In development mode, return a mock user
    if (process.env.NODE_ENV === 'development') {
      return {
        id: userId,
        email: '<EMAIL>',
        firstName: 'Test',
        lastName: 'User'
      };
    }

    // In production, use the Clerk API
    // This would need to be implemented with the correct Clerk API
    return null;
  } catch (error) {
    console.error('Error fetching user:', error);
    return null;
  }
}
