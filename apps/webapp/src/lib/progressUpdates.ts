import { getIO } from './socketServer';

export interface ProgressUpdate {
  status: 'idle' | 'processing' | 'completed' | 'error';
  current: string;
  processed: number;
  total: number;
  cachedCount?: number;
  newAnalysisCount?: number;
  remainingTokens?: number;
  error?: string;
}

/**
 * Send a progress update to a specific user via Socket.IO
 * @param userId The user ID to send the update to
 * @param progressData The progress data to send
 */
export function sendProgressUpdate(userId: string, progressData: ProgressUpdate) {
  try {
    const io = getIO();

    // Log the progress update
    console.log(`Sending progress update to user ${userId}:`,
      JSON.stringify({
        ...progressData,
        status: progressData.status,
        processed: progressData.processed,
        total: progressData.total
      })
    );

    // Get all socket IDs in the user's room
    const userRoom = `user:${userId}`;
    const socketsInRoom = io.sockets.adapter.rooms.get(userRoom);

    if (!socketsInRoom || socketsInRoom.size === 0) {
      console.warn(`No sockets found in room ${userRoom} for user ${userId}`);

      // Try broadcasting to all sockets as a fallback
      console.log(`Attempting to broadcast progress update to all sockets for user ${userId}`);
      io.emit('progress', {
        ...progressData,
        _broadcast: true // Add a flag to indicate this was broadcast to all sockets
      });
      return;
    }

    console.log(`Found ${socketsInRoom.size} socket(s) in room ${userRoom} for user ${userId}`);

    // Send to the user's room
    io.to(userRoom).emit('progress', progressData);

    // Also log which sockets received the update
    const socketIds = Array.from(socketsInRoom);
    console.log(`Sent progress update to sockets: ${socketIds.join(', ')}`);
  } catch (error) {
    console.error(`Failed to send progress update to user ${userId}:`, error);
  }
}

/**
 * Send an error update to a specific user
 * @param userId The user ID to send the error to
 * @param message The error message
 */
export function sendErrorUpdate(userId: string, message: string) {
  sendProgressUpdate(userId, {
    status: 'error',
    current: message,
    processed: 0,
    total: 0,
    error: message
  });
}
