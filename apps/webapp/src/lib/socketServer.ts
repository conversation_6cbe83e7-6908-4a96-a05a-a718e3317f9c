import { Server as SocketIOServer } from 'socket.io';
import { Server as HTTPServer } from 'http';
import { Server as HTTPSServer } from 'https';
import { verifyToken } from './auth';

// Define the global augmentation for TypeScript
declare global {
  var io: SocketIOServer | undefined;
}

export function initSocketServer(server: HTTPServer | HTTPSServer) {
  const io = new SocketIOServer(server, {
    path: '/api/socket',
    cors: {
      origin: process.env.NODE_ENV === 'production'
        ? process.env.NEXT_PUBLIC_APP_URL || 'https://yourdomain.com'
        : 'http://localhost:3000',
      methods: ['GET', 'POST']
    }
  });

  // Authentication middleware
  io.use(async (socket, next) => {
    // Try to get token from auth object first
    let token = socket.handshake.auth.token;

    // If not in auth, try to get from query params (less secure but useful for debugging)
    if (!token && socket.handshake.query.token) {
      token = socket.handshake.query.token as string;
    }

    if (!token) {
      console.log('Socket authentication failed: No token provided');
      return next(new Error('Authentication error: No token provided'));
    }

    try {
      const userId = await verifyToken(token);
      if (!userId) {
        console.log('Socket authentication failed: Invalid token');
        return next(new Error('Invalid token'));
      }

      // Store userId in socket for later use
      socket.data.userId = userId;
      console.log(`Socket authenticated for user ${userId}`);
      next();
    } catch (error) {
      console.error('Socket authentication error:', error);
      next(new Error('Authentication error'));
    }
  });

  // Connection handler
  io.on('connection', (socket) => {
    const userId = socket.data.userId;
    console.log(`User ${userId} connected via socket`);

    // Join a room specific to this user
    socket.join(`user:${userId}`);

    // Send initial connection confirmation
    socket.emit('progress', {
      status: 'idle',
      current: 'Connected successfully. Ready for analysis.',
      processed: 0,
      total: 0,
      cachedCount: 0
    });

    socket.on('disconnect', () => {
      console.log(`User ${userId} disconnected from socket`);
    });
  });

  // Store io instance globally for access from other parts of the app
  global.io = io;

  return io;
}

// Helper to get the io instance
export function getIO(): SocketIOServer {
  if (!global.io) {
    throw new Error('Socket.IO has not been initialized. Please call initSocketServer first.');
  }
  return global.io;
}
