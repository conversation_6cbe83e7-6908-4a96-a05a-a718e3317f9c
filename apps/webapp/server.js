const { createServer } = require('http');
const { parse } = require('url');
const next = require('next');
const path = require('path');

// Dynamically import the socket server (TypeScript file)
async function importSocketServer() {
  try {
    // For production, we need to use the compiled JS file from .next directory
    if (process.env.NODE_ENV === 'production') {
      // Try to import from the Next.js build output
      try {
        const { initSocketServer } = require('./.next/server/lib/socketServer');
        return { initSocketServer };
      } catch (nextError) {
        console.warn('Could not import from .next directory, trying alternative paths:', nextError.message);
        // Fallback: try to import the source file directly
        const { initSocketServer } = require('./src/lib/socketServer');
        return { initSocketServer };
      }
    } else {
      // For development, we can use ts-node to import the TS file directly
      require('ts-node').register({
        transpileOnly: true,
        compilerOptions: {
          module: 'commonjs',
          target: 'es2017',
          moduleResolution: 'node',
        },
      });
      const { initSocketServer } = require('./src/lib/socketServer');
      return { initSocketServer };
    }
  } catch (error) {
    console.error('Error importing socket server:', error);
    console.error('Continuing without Socket.io server - real-time features will be disabled');
    // Return a mock function instead of exiting
    return {
      initSocketServer: (server) => {
        console.warn('Socket.io server disabled due to import error');
        return null;
      }
    };
  }
}

// Start the server
async function startServer() {
  const dev = process.env.NODE_ENV !== 'production';
  const hostname = 'localhost';
  const port = process.env.PORT || 3000;

  // Initialize Next.js
  const app = next({ dev, hostname, port });
  const handle = app.getRequestHandler();

  try {
    await app.prepare();

    // Import the socket server
    const { initSocketServer } = await importSocketServer();

    // Create HTTP server
    const server = createServer((req, res) => {
      const parsedUrl = parse(req.url, true);
      handle(req, res, parsedUrl);
    });

    // Initialize Socket.IO with our HTTP server
    initSocketServer(server);

    // Start listening
    server.listen(port, (err) => {
      if (err) throw err;
      console.log(`> Ready on http://${hostname}:${port}`);
    });
  } catch (error) {
    console.error('Error starting server:', error);
    process.exit(1);
  }
}

startServer();
