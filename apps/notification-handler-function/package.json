{"name": "notification-handler-function", "version": "1.0.0", "description": "GCP Cloud Function for Gmail notification handling", "main": "dist/main.js", "scripts": {"start": "node dist/main.js", "build": "tsc", "deploy": "gcloud run deploy handle-gmail-notification --source=. --platform=managed --region=us-central1 --allow-unauthenticated --port=8080"}, "dependencies": {"@google-cloud/pubsub": "^4.0.0", "@webapp/email-core": "file:../../libs/email-core", "@webapp/services": "file:../../libs/services", "express": "^4.18.0", "tslib": "^2.3.0"}, "devDependencies": {"@types/express": "^4.17.0", "@types/node": "^18.0.0", "typescript": "^5.0.0"}, "engines": {"node": "20"}}