// Standard GCP Cloud Function imports (no functions-framework needed for Pub/Sub)
import { EmailAnalyzer } from '../../../libs/email-core/src/index';
import { AuthService, Database, Logger, TokenService } from '../../../libs/services/src/index';
import type { GmailService as ServicesGmailService, TokenService as ServicesTokenService } from '../../../libs/services/src/index';
import type { EmailContent, GmailService as EmailCoreGmailService, TokenService as EmailCoreTokenService } from '../../../libs/email-core/src/index';

// Initialize services
const logger = new Logger();
const database = new Database();

// Adapter class to bridge the gap between services GmailService and email-core GmailService interfaces
class GmailServiceAdapter implements EmailCoreGmailService {
  constructor(private servicesGmailService: ServicesGmailService) {}

  async getEmailContent(messageId: string): Promise<EmailContent> {
    // Get the email content from the services Gmail service
    const content = await this.servicesGmailService.getEmailContent(messageId);

    if (!content) {
      throw new Error(`Email content not found for message ${messageId}`);
    }

    // Get additional email metadata using Gmail API
    const gmail = (this.servicesGmailService as any).gmail;
    const messageResponse = await gmail.users.messages.get({
      userId: 'me',
      id: messageId,
      format: 'metadata',
      metadataHeaders: ['Subject', 'From', 'To']
    });

    const headers = messageResponse.data.payload?.headers || [];
    const subject = headers.find((h: any) => h.name === 'Subject')?.value || '';
    const from = headers.find((h: any) => h.name === 'From')?.value || '';
    const to = headers.find((h: any) => h.name === 'To')?.value || '';

    // Convert to the expected EmailContent format
    return {
      subject,
      body: content.text,
      from,
      to,
      date: content.date
    };
  }

  getMonitoredEmail(): string {
    return this.servicesGmailService.getMonitoredEmail();
  }

  async modifyMessage(messageId: string, options: { addLabelIds?: string[], removeLabelIds?: string[] }): Promise<void> {
    return this.servicesGmailService.modifyMessage(messageId, options);
  }

  async getDDJSLabelIds(): Promise<Record<string, string>> {
    return this.servicesGmailService.getDDJSLabelIds();
  }
}

// Adapter class to bridge the gap between services TokenService and email-core TokenService interfaces
class TokenServiceAdapter implements EmailCoreTokenService {
  constructor(private servicesTokenService: ServicesTokenService) {}

  async getRemainingTokens(clerkUserId: string): Promise<number> {
    return this.servicesTokenService.getRemainingTokens(clerkUserId);
  }

  async deductTokens(clerkUserId: string, amount: number): Promise<void> {
    // The services TokenService returns the remaining tokens, but email-core expects void
    await this.servicesTokenService.deductTokens(clerkUserId, amount);
  }
}

// Function to send progress updates back to the webapp
async function sendProgressUpdate(
  clerkUserId: string,
  messageId: string,
  status: 'processing' | 'completed' | 'error',
  current?: string,
  analysis?: any,
  error?: string
) {
  try {
    // TODO: Replace with actual webapp URL
    const webhookUrl = process.env.WEBAPP_PROGRESS_WEBHOOK_URL || 'http://localhost:3000/api/emails/progress-webhook';

    const response = await fetch(webhookUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        clerkUserId,
        messageId,
        status,
        current,
        analysis,
        error
      })
    });

    if (!response.ok) {
      logger.warn('Failed to send progress update', {
        status: response.status,
        statusText: response.statusText
      });
    }
  } catch (error) {
    logger.error('Error sending progress update', {
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
}

// Helper function to update job progress in database
async function updateJobProgress(jobId: string, updates: any) {
  if (!jobId) return; // Skip if no jobId provided (backward compatibility)

  try {
    await database.collection('analysisJobs').doc(jobId).update({
      ...updates,
      updatedAt: new Date()
    });
    logger.info('Updated job progress', { jobId, updates });
  } catch (error) {
    logger.error('Failed to update job progress', {
      jobId,
      error: error instanceof Error ? error.message : String(error)
    });
  }
}

// Helper function to initialize services for a user
async function initializeServicesForUser(clerkUserId: string) {
  try {
    // Initialize auth service and get user's Gmail service
    const authService = new AuthService(logger);
    const servicesGmailService = await authService.getGmailClientForUser(clerkUserId);

    // Create adapters to bridge the interface gaps
    const gmailService = new GmailServiceAdapter(servicesGmailService);

    // Initialize token service and create adapter
    const servicesTokenService = new TokenService(database, logger);
    const tokenService = new TokenServiceAdapter(servicesTokenService);

    logger.info('Services initialized successfully', {
      clerkUserId,
      monitoredEmail: gmailService.getMonitoredEmail()
    });

    return { tokenService, gmailService };
  } catch (error) {
    logger.error('Failed to initialize services', {
      clerkUserId,
      error: error instanceof Error ? error.message : String(error)
    });
    throw error;
  }
}

// GCP Cloud Function that triggers on Pub/Sub messages for email analysis
// Standard GCP Pub/Sub function signature
exports.analyzeEmail = async (pubsubMessage: any, context: any) => {
  let clerkUserId: string = '';
  let messageId: string = '';
  let jobId: string = '';

  try {
    // Extract Pub/Sub message data (GCP standard format)
    const messageData = pubsubMessage.data;
    if (!messageData) {
      throw new Error('No message data found in Pub/Sub message');
    }

    const decodedData = Buffer.from(messageData, 'base64').toString();
    const parsedData = JSON.parse(decodedData);
    clerkUserId = parsedData.clerkUserId;
    messageId = parsedData.messageId;
    jobId = parsedData.jobId;
    const monitoredEmail = parsedData.monitoredEmail;
    const batchIndex = parsedData.batchIndex;
    const totalInBatch = parsedData.totalInBatch;

    logger.info('Processing email analysis request', {
      clerkUserId,
      messageId,
      monitoredEmail,
      jobId,
      batchIndex,
      totalInBatch,
      eventId: context.eventId,
      timestamp: context.timestamp
    });

    // Initialize services with real implementations
    const emailAnalyzer = new EmailAnalyzer();
    const { tokenService, gmailService } = await initializeServicesForUser(clerkUserId);

    // Get DDJS label IDs for email categorization
    const labelIds = await gmailService.getDDJSLabelIds();

    // Update job progress
    await updateJobProgress(jobId, {
      current: `Analyzing email ${batchIndex + 1} of ${totalInBatch}...`,
      processed: batchIndex
    });

    // Send progress update to webapp (keep for backward compatibility)
    await sendProgressUpdate(clerkUserId, messageId, 'processing', 'Analyzing email content...')

    // Process the email
    const analysis = await emailAnalyzer.processEmail(
      { clerkUserId, messageId },
      gmailService,
      tokenService,
      labelIds
    );

    logger.info('Email analysis completed', {
      clerkUserId,
      messageId,
      company: analysis.parsed?.company_name,
      category: analysis.parsed?.email_type_category,
      isJobSearch: analysis.parsed?.is_related_to_job_search
    });

    // Update job progress with completion
    await updateJobProgress(jobId, {
      current: `Completed email ${batchIndex + 1} of ${totalInBatch}`,
      processed: batchIndex + 1,
      // If this is the last email, mark job as completed
      ...(batchIndex + 1 === totalInBatch ? {
        status: 'completed',
        completedAt: new Date(),
        current: 'All emails analyzed successfully'
      } : {})
    });

    // Send completion update to webapp (keep for backward compatibility)
    await sendProgressUpdate(clerkUserId, messageId, 'completed', 'Email analysis completed', analysis.parsed);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';

    logger.error('Failed to process email analysis', {
      error: errorMessage,
      stack: error instanceof Error ? error.stack : undefined
    });

    // Update job status with error
    try {
      await updateJobProgress(jobId, {
        status: 'error',
        current: `Error analyzing email: ${errorMessage}`,
        error: errorMessage
      });
    } catch (jobUpdateError) {
      logger.error('Failed to update job with error status', { jobUpdateError });
    }

    // Send error update to webapp (keep for backward compatibility)
    try {
      await sendProgressUpdate(clerkUserId, messageId, 'error', 'Email analysis failed', undefined, errorMessage);
    } catch (webhookError) {
      logger.error('Failed to send error update', { webhookError });
    }

    throw error;
  }
};
